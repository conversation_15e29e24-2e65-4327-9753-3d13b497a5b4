<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Access - BitBot | Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="assets/favicon.ico">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="login.css">

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, onAuthStateChanged } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        
        const firebaseConfig = {
            apiKey: "AIzaSyA0gXeFSshTDc-tAlXYHK_xezkNblz7GMg",
            authDomain: "bitbot-d967b.firebaseapp.com",
            projectId: "bitbot-d967b",
            storageBucket: "bitbot-d967b.firebasestorage.app",
            messagingSenderId: "416388708240",
            appId: "1:416388708240:web:dbbc2c271ca15e36cfe515",
            measurementId: "G-9L48V12SBB"
        };

        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        
        // Check if user is already authenticated
        onAuthStateChanged(auth, (user) => {
            if (user) {
                // Check if user is admin
                const isAdmin = user.email && (
                    user.email.toLowerCase().includes('@bit.com') ||
                    user.email.toLowerCase().includes('@admin.bit.com') ||
                    user.email.toLowerCase().startsWith('admin@') ||
                    user.email.toLowerCase().includes('.admin@')
                );
                
                if (isAdmin) {
                    // Redirect to admin panel
                    window.location.href = 'admin-panel.html';
                } else {
                    // Not an admin, redirect to student dashboard
                    window.location.href = 'student-dashboard.html';
                }
            } else {
                // Not authenticated, show admin login
                window.location.href = 'admin-login.html';
            }
        });
    </script>
</head>
<body>
    <div class="login-container">
        <!-- Loading Screen -->
        <div class="login-card">
            <div class="login-header">
                <div class="logo-section">
                    <div class="bot-avatar">🛡️</div>
                    <h1 class="login-title">BitBot Admin</h1>
                    <p class="login-subtitle">Checking Authentication...</p>
                </div>
                <p class="institute-name">Bhagwant Institute of Technology</p>
            </div>

            <div style="text-align: center; padding: 40px 0;">
                <div class="spinner" style="margin: 0 auto; width: 40px; height: 40px; border: 3px solid #f3f3f3; border-top: 3px solid #000; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                <p style="margin-top: 20px; color: #666; font-size: 14px;">Redirecting to appropriate page...</p>
            </div>
        </div>

        <!-- Copyright -->
        <div class="copyright">
            <p>&copy; 2025 BitBot | Developed by Akash Prajapati, BCA 3rd Year</p>
        </div>
    </div>

    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</body>
</html>
