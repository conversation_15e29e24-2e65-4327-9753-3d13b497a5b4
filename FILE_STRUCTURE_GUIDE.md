# BitBot File Structure & Access Guide

## 🌐 **New Public Access Model**

The BitBot system has been restructured to provide **public access** with **conditional authentication** for sensitive information.

---

## 📁 **File Structure**

### **Public Access Files:**
- `index.html` - **Main public chatbot interface** (no login required)
- `style.css` - Main chatbot styling
- `script.js` - Enhanced chatbot logic with query classification

### **Authentication Files:**
- `login.html` - Student login page
- `admin-login.html` - Admin login page  
- `admin.html` - Admin route handler (`/admin` redirect)
- `login.css` - Login page styling
- `auth.js` - Complete authentication system

### **Protected Dashboards:**
- `student-dashboard.html` - Authenticated student portal
- `admin-panel.html` - Admin management dashboard

### **Assets & Scripts:**
- `assets/` - Favicons and images
- `css/dash-style.css` - Admin panel styling
- `js/dash-script.js` - Admin panel functionality

---

## 🚀 **Access Flow**

### **1. Public Access (https://bitbot.com)**
- **File:** `index.html`
- **Access:** No authentication required
- **Features:**
  - Full-screen chatbot interface
  - Answers public queries directly
  - Prompts login for private queries

### **2. Admin Access (https://bitbot.com/admin)**
- **File:** `admin.html` (redirects appropriately)
- **Flow:**
  - If not logged in → `admin-login.html`
  - If logged in as admin → `admin-panel.html`
  - If logged in as student → `student-dashboard.html`

### **3. Student Login**
- **File:** `login.html`
- **Redirect:** After login → `student-dashboard.html`

### **4. Admin Login**
- **File:** `admin-login.html`
- **Redirect:** After login → `admin-panel.html`

---

## 🤖 **Query Classification System**

### **Public Queries (Answered Directly):**
✅ "What is today's timetable?"
✅ "What are the class timings?"
✅ "What is the course fee for BCA?"
✅ "Who is the HOD of the IT department?"
✅ "What are the lab timings?"
✅ "How to contact the office?"

### **Private Queries (Require Login):**
🔒 "What is my fee due?"
🔒 "Show my marks"
🔒 "Show my profile"
🔒 "My attendance record"
🔒 "My exam results"
🔒 "Check my account status"

---

## 🔧 **Technical Implementation**

### **Query Detection Logic:**
```javascript
// Private query keywords
const privateKeywords = [
    'my fee', 'fee due', 'my marks', 'my profile', 
    'my attendance', 'my exam', 'my result', 
    'show my', 'display my', 'check my'
];
```

### **Response System:**
- **Public queries** → Direct answers from knowledge base
- **Private queries** → Login prompt with clickable link
- **Authenticated users** → Access to personal data

---

## 🎯 **User Experience**

### **For Anonymous Users:**
1. Visit `https://bitbot.com`
2. See full chatbot interface immediately
3. Ask public questions → Get instant answers
4. Ask private questions → Get login prompt with link

### **For Students:**
1. Click login link or visit `login.html`
2. Enter credentials
3. Redirect to `student-dashboard.html`
4. Access personal information

### **For Admins:**
1. Visit `https://bitbot.com/admin`
2. Automatic redirect to `admin-login.html`
3. Enter admin credentials
4. Redirect to `admin-panel.html`

---

## 🔒 **Security Features**

### **Authentication Guards:**
- `student-dashboard.html` - Requires student authentication
- `admin-panel.html` - Requires admin authentication
- Public pages - No authentication required

### **Role Detection:**
- Admin emails: `@bit.com`, `@admin.bit.com`, `admin@*`, `*.admin@*`
- Student emails: All other valid emails

### **Session Management:**
- Real-time authentication state monitoring
- Automatic redirects based on auth status
- Secure logout functionality

---

## 🧪 **Testing Scenarios**

### **Test Public Access:**
1. Open `index.html` directly
2. Try queries: "What are class timings?"
3. Should get direct answers

### **Test Private Query Handling:**
1. On public page, ask: "What is my fee due?"
2. Should see login prompt with clickable link
3. Link should redirect to `login.html`

### **Test Admin Route:**
1. Visit `admin.html`
2. Should redirect to `admin-login.html` if not authenticated
3. After admin login, should redirect to `admin-panel.html`

### **Test Authentication Flow:**
1. Login as student → Should redirect to `student-dashboard.html`
2. Login as admin → Should redirect to `admin-panel.html`
3. Logout → Should return to appropriate page

---

## 📋 **Setup Checklist**

### **Firebase Configuration:**
- ✅ Firebase project configured
- ✅ Authentication enabled
- ✅ Email/Password provider enabled
- ✅ Test users created

### **File Deployment:**
- ✅ All HTML files in root directory
- ✅ CSS files properly linked
- ✅ JavaScript files loaded correctly
- ✅ Assets folder with favicons

### **URL Routing:**
- ✅ `https://bitbot.com` → `index.html`
- ✅ `https://bitbot.com/admin` → `admin.html`
- ✅ All other routes work correctly

---

## 🚨 **Important Notes**

1. **Public First:** The main page is now public and doesn't require login
2. **Smart Routing:** Authentication is only required for sensitive information
3. **Admin Access:** Direct `/admin` route for administrative access
4. **Backward Compatibility:** Existing authentication system still works
5. **Enhanced UX:** Users can get help immediately without barriers

---

## 📞 **Support & Maintenance**

### **Common Issues:**
- **Query not classified correctly** → Update keywords in `auth.js`
- **Wrong redirect after login** → Check role detection logic
- **Public queries not working** → Verify `handlePublicQuery()` function

### **Adding New Public Responses:**
1. Edit `handlePublicQuery()` function in `auth.js`
2. Add new query patterns and responses
3. Test with various phrasings

### **Adding New Private Keywords:**
1. Edit `isPrivateQuery()` function in `auth.js`
2. Add keywords to `privateKeywords` array
3. Test detection accuracy

---

**Developed by Akash Prajapati, BCA 3rd Year – Bhagwant Institute of Technology**
