<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - BitBot | Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="assets/favicon.ico">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="login.css">

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        
        const firebaseConfig = {
            apiKey: "AIzaSyA0gXeFSshTDc-tAlXYHK_xezkNblz7GMg",
            authDomain: "bitbot-d967b.firebaseapp.com",
            projectId: "bitbot-d967b",
            storageBucket: "bitbot-d967b.firebasestorage.app",
            messagingSenderId: "416388708240",
            appId: "1:416388708240:web:dbbc2c271ca15e36cfe515",
            measurementId: "G-9L48V12SBB"
        };

        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        
        // Make auth available globally
        window.firebaseAuth = auth;
    </script>
</head>
<body>
    <div class="login-container admin-theme">
        <!-- Background Pattern -->
        <div class="bg-pattern"></div>
        
        <!-- Login Card -->
        <div class="login-card">
            <!-- Header -->
            <div class="login-header">
                <div class="logo-section">
                    <div class="bot-avatar admin-avatar">🛡️</div>
                    <h1 class="login-title">BitBot Admin</h1>
                    <p class="login-subtitle">Administrative Portal</p>
                </div>
                <p class="institute-name">Bhagwant Institute of Technology</p>
            </div>

            <!-- Login Form -->
            <form class="login-form" id="adminLoginForm">
                <div class="form-group">
                    <label for="adminEmail" class="form-label">Admin Email</label>
                    <div class="input-wrapper">
                        <svg class="input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                        </svg>
                        <input 
                            type="email" 
                            id="adminEmail" 
                            name="email" 
                            placeholder="Enter admin email address"
                            required
                            autocomplete="email"
                        >
                    </div>
                </div>

                <div class="form-group">
                    <label for="adminPassword" class="form-label">Admin Password</label>
                    <div class="input-wrapper">
                        <svg class="input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                            <circle cx="12" cy="16" r="1"></circle>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                        </svg>
                        <input 
                            type="password" 
                            id="adminPassword" 
                            name="password" 
                            placeholder="Enter admin password"
                            required
                            autocomplete="current-password"
                        >
                        <button type="button" class="password-toggle" id="adminPasswordToggle">
                            <svg class="eye-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Error Message -->
                <div class="error-message" id="adminErrorMessage" style="display: none;">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="15" y1="9" x2="9" y2="15"></line>
                        <line x1="9" y1="9" x2="15" y2="15"></line>
                    </svg>
                    <span id="adminErrorText">Invalid admin credentials. Please try again.</span>
                </div>

                <!-- Login Button -->
                <button type="submit" class="login-btn admin-btn" id="adminLoginBtn">
                    <span class="btn-text">Admin Sign In</span>
                    <div class="btn-loader" style="display: none;">
                        <div class="spinner"></div>
                    </div>
                </button>

                <!-- Security Notice -->
                <div class="security-notice">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                    </svg>
                    <span>Secure admin access only</span>
                </div>
            </form>

            <!-- Footer -->
            <div class="login-footer">
                <p>Authorized personnel only</p>
                <div class="student-link">
                    <a href="login.html">Student Login</a>
                </div>
                <div style="margin-top: 12px;">
                    <a href="index.html" style="color: #666; text-decoration: none; font-size: 14px;">← Back to Public Chatbot</a>
                </div>
            </div>
        </div>

        <!-- Copyright -->
        <div class="copyright">
            <p>&copy; 2025 BitBot | Developed by Akash Prajapati, BCA 3rd Year</p>
        </div>
    </div>

    <script src="auth.js"></script>
    <script>
        // Initialize admin login
        if (typeof initializeAdminLogin === 'function') {
            initializeAdminLogin();
        }
    </script>
</body>
</html>
