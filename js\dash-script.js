// Admin Panel JavaScript
class AdminPanel {
    constructor() {
        this.data = [
            {
                id: 1,
                category: 'timetable',
                title: 'BCA 3rd Year - Monday Schedule',
                description: 'Database Management Systems (9:00-10:30), Web Development (11:00-12:30), Software Engineering (2:00-3:30)',
                dateAdded: '2025-01-15'
            },
            {
                id: 2,
                category: 'fee',
                title: 'Semester Fee Structure 2025',
                description: 'Tuition Fee: ₹45,000, Lab Fee: ₹5,000, Library Fee: ₹2,000, Total: ₹52,000 per semester',
                dateAdded: '2025-01-10'
            },
            {
                id: 3,
                category: 'faculty',
                title: 'Dr. <PERSON><PERSON> - Computer Science',
                description: 'Professor of Computer Science, PhD in AI/ML, 15+ years experience. Office: Room 201, Contact: <EMAIL>',
                dateAdded: '2025-01-08'
            },
            {
                id: 4,
                category: 'notice',
                title: 'Mid-Semester Examination Schedule',
                description: 'Mid-semester exams will be conducted from March 15-25, 2025. Students must carry valid ID cards and admit cards.',
                dateAdded: '2025-01-12'
            }
        ];
        
        this.currentEditId = null;
        this.isDarkMode = false;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.updateStats();
        this.renderTable();
        this.initTheme();
    }
    
    bindEvents() {
        // Navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => this.switchSection(e.target.dataset.section));
        });
        
        // Mobile menu toggle
        document.getElementById('mobileMenuToggle').addEventListener('click', () => {
            document.getElementById('sidebar').classList.toggle('active');
        });
        
        // Theme toggle
        document.getElementById('themeToggle').addEventListener('click', () => this.toggleTheme());
        document.getElementById('themeSettingToggle').addEventListener('click', () => this.toggleTheme());
        
        // Form submission
        document.getElementById('addForm').addEventListener('submit', (e) => this.handleAddForm(e));
        document.getElementById('editForm').addEventListener('submit', (e) => this.handleEditForm(e));
        
        // Modal controls
        document.getElementById('closeModal').addEventListener('click', () => this.closeModal());
        document.getElementById('cancelEdit').addEventListener('click', () => this.closeModal());
        document.getElementById('editModal').addEventListener('click', (e) => {
            if (e.target.id === 'editModal') this.closeModal();
        });
        
        // Search and filter
        document.getElementById('searchInput').addEventListener('input', (e) => this.filterTable(e.target.value));
        document.getElementById('filterCategory').addEventListener('change', (e) => this.filterTable(null, e.target.value));
        
        // Logout button
        document.getElementById('logoutBtn').addEventListener('click', () => {
            if (confirm('Are you sure you want to logout?')) {
                alert('Logout functionality would redirect to login page');
            }
        });
        
        // Settings toggles
        document.getElementById('autosaveToggle').addEventListener('click', (e) => {
            e.target.classList.toggle('active');
            // Update accessibility attributes
            const isActive = e.target.classList.contains('active');
            e.target.setAttribute('aria-pressed', isActive);
            e.target.setAttribute('aria-label', `Auto-save is ${isActive ? 'enabled' : 'disabled'}`);
        });
    }
    
    switchSection(sectionName) {
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');
        
        // Update content
        document.querySelectorAll('.content-section').forEach(section => section.classList.remove('active'));
        document.getElementById(`${sectionName}-section`).classList.add('active');
        
        // Close mobile menu
        document.getElementById('sidebar').classList.remove('active');
    }
    
    toggleTheme() {
        this.isDarkMode = !this.isDarkMode;
        document.body.classList.toggle('dark-mode', this.isDarkMode);

        // Update theme setting toggle
        const themeSettingToggle = document.getElementById('themeSettingToggle');
        themeSettingToggle.classList.toggle('active', this.isDarkMode);

        // Update accessibility attributes
        themeSettingToggle.setAttribute('aria-pressed', this.isDarkMode);
        themeSettingToggle.setAttribute('aria-label', `Theme is ${this.isDarkMode ? 'dark mode' : 'light mode'}`);

        // Save theme preference
        localStorage.setItem('adminTheme', this.isDarkMode ? 'dark' : 'light');
    }
    
    initTheme() {
        const savedTheme = localStorage.getItem('adminTheme');
        const themeSettingToggle = document.getElementById('themeSettingToggle');

        if (savedTheme === 'dark') {
            this.isDarkMode = true;
            document.body.classList.add('dark-mode');
            themeSettingToggle.classList.add('active');
        }

        // Initialize accessibility attributes
        themeSettingToggle.setAttribute('aria-pressed', this.isDarkMode);
        themeSettingToggle.setAttribute('aria-label', `Theme is ${this.isDarkMode ? 'dark mode' : 'light mode'}`);
        themeSettingToggle.setAttribute('role', 'switch');

        // Initialize autosave toggle accessibility
        const autosaveToggle = document.getElementById('autosaveToggle');
        const isAutosaveActive = autosaveToggle.classList.contains('active');
        autosaveToggle.setAttribute('aria-pressed', isAutosaveActive);
        autosaveToggle.setAttribute('aria-label', `Auto-save is ${isAutosaveActive ? 'enabled' : 'disabled'}`);
        autosaveToggle.setAttribute('role', 'switch');
    }
    
    handleAddForm(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const newRecord = {
            id: this.data.length > 0 ? Math.max(...this.data.map(item => item.id)) + 1 : 1,
            category: formData.get('category'),
            title: formData.get('title'),
            description: formData.get('description'),
            dateAdded: new Date().toISOString().split('T')[0]
        };
        
        this.data.push(newRecord);
        this.updateStats();
        this.renderTable();
        
        // Reset form
        e.target.reset();
        
        // Show success message
        this.showNotification('Record added successfully!', 'success');
        
        // Switch to manage data section
        this.switchSection('manage-data');
    }
    
    handleEditForm(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const recordIndex = this.data.findIndex(item => item.id === this.currentEditId);
        
        if (recordIndex !== -1) {
            this.data[recordIndex] = {
                ...this.data[recordIndex],
                category: formData.get('category'),
                title: formData.get('title'),
                description: formData.get('description')
            };
            
            this.updateStats();
            this.renderTable();
            this.closeModal();
            
            this.showNotification('Record updated successfully!', 'success');
        }
    }
    
    editRecord(id) {
        const record = this.data.find(item => item.id === id);
        if (!record) return;
        
        this.currentEditId = id;
        
        // Populate form
        document.getElementById('editId').value = record.id;
        document.getElementById('editCategory').value = record.category;
        document.getElementById('editTitle').value = record.title;
        document.getElementById('editDescription').value = record.description;
        
        // Show modal
        document.getElementById('editModal').classList.add('active');
    }
    
    deleteRecord(id) {
        if (confirm('Are you sure you want to delete this record?')) {
            this.data = this.data.filter(item => item.id !== id);
            this.updateStats();
            this.renderTable();
            
            this.showNotification('Record deleted successfully!', 'success');
        }
    }
    
    closeModal() {
        document.getElementById('editModal').classList.remove('active');
        this.currentEditId = null;
    }
    
    updateStats() {
        const totalRecords = this.data.length;
        const timetableCount = this.data.filter(item => item.category === 'timetable').length;
        const feeCount = this.data.filter(item => item.category === 'fee').length;
        const facultyCount = this.data.filter(item => item.category === 'faculty').length;
        
        document.getElementById('totalRecords').textContent = totalRecords;
        document.getElementById('timetableCount').textContent = timetableCount;
        document.getElementById('feeCount').textContent = feeCount;
        document.getElementById('facultyCount').textContent = facultyCount;
    }
    
    renderTable(filteredData = null) {
        const dataToRender = filteredData || this.data;
        const tableBody = document.getElementById('tableBody');
        
        if (dataToRender.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="6" style="text-align: center; padding: 2rem; color: #666;">
                        No records found
                    </td>
                </tr>
            `;
            return;
        }
        
        tableBody.innerHTML = dataToRender.map(record => `
            <tr>
                <td>${record.id}</td>
                <td>
                    <span class="category-badge category-${record.category}">
                        ${this.getCategoryIcon(record.category)} ${this.getCategoryName(record.category)}
                    </span>
                </td>
                <td>${record.title}</td>
                <td>${this.truncateText(record.description, 100)}</td>
                <td>${this.formatDate(record.dateAdded)}</td>
                <td>
                    <div class="action-buttons">
                        <button class="edit-btn" onclick="adminPanel.editRecord(${record.id})" title="Edit">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                            </svg>
                        </button>
                        <button class="delete-btn" onclick="adminPanel.deleteRecord(${record.id})" title="Delete">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="3,6 5,6 21,6"></polyline>
                                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                <line x1="10" y1="11" x2="10" y2="17"></line>
                                <line x1="14" y1="11" x2="14" y2="17"></line>
                            </svg>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    filterTable(searchTerm = null, category = null) {
        const search = searchTerm || document.getElementById('searchInput').value.toLowerCase();
        const categoryFilter = category || document.getElementById('filterCategory').value;
        
        let filteredData = this.data;
        
        if (search) {
            filteredData = filteredData.filter(record => 
                record.title.toLowerCase().includes(search) ||
                record.description.toLowerCase().includes(search) ||
                record.category.toLowerCase().includes(search)
            );
        }
        
        if (categoryFilter) {
            filteredData = filteredData.filter(record => record.category === categoryFilter);
        }
        
        this.renderTable(filteredData);
    }
    
    getCategoryIcon(category) {
        const icons = {
            'timetable': '📅',
            'fee': '💰',
            'faculty': '👨‍🏫',
            'notice': '📢'
        };
        return icons[category] || '📄';
    }
    
    getCategoryName(category) {
        const names = {
            'timetable': 'Timetable',
            'fee': 'Fee Information',
            'faculty': 'Faculty Information',
            'notice': 'Notice/Announcement'
        };
        return names[category] || category;
    }
    
    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }
    
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }
    
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span>${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Show notification
        setTimeout(() => notification.classList.add('show'), 100);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
        
        // Manual close
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        });
    }
}

// Initialize admin panel when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.adminPanel = new AdminPanel();
});

// Handle escape key for modal
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && document.getElementById('editModal').classList.contains('active')) {
        window.adminPanel.closeModal();
    }
});
